/**
 * ThreadManager Hook
 *
 * Used in: frontend/components/panel/ConversationPanel.tsx
 * Purpose: Custom hook that manages thread operations including navigation, deletion, and active state.
 * Provides thread data and operations for the conversation panel.
 */

import { useState, useEffect, useCallback } from 'react';
import { useNavigate, useParams } from 'react-router';
import { Thread } from '@/lib/appwriteDB';
import { HybridDB, dbEvents } from '@/lib/hybridDB';
import { useOptimizedThreads } from '@/frontend/hooks/useOptimizedHybridDB';
import { useIsMobile } from '@/hooks/useMobileDetection';
import { useOutletContext } from 'react-router-dom';

// Custom hook for managing thread operations
export const useThreadManager = () => {
  const { id: currentThreadId } = useParams();
  const router = useNavigate();
  const isMobile = useIsMobile();
  
  // Get sidebar controls from outlet context
  const outletContext = useOutletContext<{
    sidebarWidth: number;
    toggleSidebar: () => void;
    state: "open" | "collapsed";
    isMobile: boolean;
  } | null>();
  
  // Use optimized hook for better performance
  const { threads: threadCollection, isLoading } = useOptimizedThreads();

  const navigateToThread = useCallback((threadId: string) => {
    if (currentThreadId === threadId) {
      return;
    }
    router(`/chat/${threadId}`);
    
    // Close sidebar on mobile after navigation
    if (isMobile && outletContext?.toggleSidebar) {
      outletContext.toggleSidebar();
    }
  }, [currentThreadId, router, isMobile, outletContext]);

  const removeThread = useCallback(async (threadId: string, event?: React.MouseEvent) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    
    try {
      // Instant local update + async backend sync
      await HybridDB.deleteThread(threadId);
      
      // Navigate away if we're deleting the current thread
      if (currentThreadId === threadId) {
        router(`/chat`);
      }
      
      // Close sidebar on mobile after deletion
      if (isMobile && outletContext?.toggleSidebar) {
        outletContext.toggleSidebar();
      }
    } catch (error) {
      console.error('Error deleting thread:', error);
      throw error; // Re-throw so the dialog can handle the error
    }
  }, [currentThreadId, router, isMobile, outletContext]);

  const isActiveThread = useCallback((threadId: string) => 
    currentThreadId === threadId, [currentThreadId]);

  return {
    currentThreadId,
    threadCollection,
    navigateToThread,
    removeThread,
    isActiveThread,
    isLoading,
  };
};

// Thread data interface
export interface ThreadData {
  id: string;
  title: string;
}

// Thread operations interface
export interface ThreadOperations {
  onNavigate: (threadId: string) => void;
  onDelete: (threadId: string, event?: React.MouseEvent) => void;
  isActive: boolean;
}
