{"name": "atchat", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "setup-appwrite": "node scripts/setup-appwrite-db.js"}, "dependencies": {"@ai-sdk/google": "^1.2.19", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/react": "^1.2.12", "@hookform/resolvers": "^5.0.1", "@openrouter/ai-sdk-provider": "^0.4.6", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@vercel/analytics": "^1.5.0", "ai": "^4.3.16", "appwrite": "^18.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dexie": "^4.0.11", "dexie-react-hooks": "^1.1.7", "fast-deep-equal": "^3.1.3", "framer-motion": "^12.17.0", "katex": "^0.16.22", "lucide-react": "^0.510.0", "marked": "^15.0.12", "next": "15.3.2", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.57.0", "react-markdown": "^10.1.0", "react-router": "^7.6.2", "react-router-dom": "^7.6.2", "react-shiki": "^0.6.0", "rehype-katex": "^7.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sonner": "^2.0.5", "swr": "^2.3.3", "tailwind-merge": "^3.3.0", "tailwind-scrollbar": "^4.0.2", "usehooks-ts": "^3.1.1", "uuid": "^11.1.0", "zod": "^3.25.56", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.8", "@tailwindcss/typography": "^0.5.16", "@types/katex": "^0.16.7", "@types/node": "^20.19.0", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "@types/react-syntax-highlighter": "^15.5.13", "eslint": "^9.28.0", "eslint-config-next": "15.3.2", "tailwindcss": "^4.1.8", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3"}}