# OpenRouter API Key
# Get your API key from: https://openrouter.ai/settings/keys
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Appwrite Configuration
# Get these from your Appwrite project settings
NEXT_PUBLIC_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
NEXT_PUBLIC_APPWRITE_PROJECT_ID=your_project_id_here
NEXT_PUBLIC_APPWRITE_DATABASE_ID=your_database_id_here
NEXT_PUBLIC_APPWRITE_THREADS_COLLECTION_ID=threads
NEXT_PUBLIC_APPWRITE_MESSAGES_COLLECTION_ID=messages
NEXT_PUBLIC_APPWRITE_MESSAGE_SUMMARIES_COLLECTION_ID=message_summaries

# Authentication URLs - Update with your domain in production
NEXT_PUBLIC_AUTH_SUCCESS_URL=http://localhost:3000/auth/callback
NEXT_PUBLIC_AUTH_FAILURE_URL=http://localhost:3000/auth/error
NEXT_PUBLIC_VERIFICATION_URL=http://localhost:3000/auth/verify
